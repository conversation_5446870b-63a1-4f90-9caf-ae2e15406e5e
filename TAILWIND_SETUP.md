# Tailwind CSS Setup in Jaspr Project

This project has been successfully configured to use Tailwind CSS with the Jaspr web framework.

## What was set up:

### 1. Tailwind CSS Integration
- Added Tailwind CSS via CDN in `lib/main.dart`
- The CDN import is included in the Document styles array: `css.import('https://cdn.tailwindcss.com')`

### 2. Custom CSS File
- Created `web/styles/tailwind.css` with custom components and utilities
- This file includes:
  - Base Tailwind directives
  - Custom component classes (`.btn-primary`, `.card`, `.nav-link`)
  - Custom utility classes (`.text-shadow`)

### 3. Updated Components
- **Home page** (`lib/pages/home.dart`): Updated to use Tailwind classes for layout and styling
- **Counter component** (`lib/components/counter.dart`): Replaced custom CSS with Tailwind utility classes
- Removed custom CSS `@css` annotations where Tailwind classes are sufficient

## How to use Tailwind CSS in Jaspr:

### Adding classes to components:
```dart
// Example: Using Tailwind classes in a div
div(classes: 'bg-blue-500 text-white p-4 rounded-lg', [
  text('This is a blue box with white text')
])

// Example: Multiple classes for responsive design
button(classes: 'px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded transition-colors duration-200', [
  text('Click me')
])
```

### Custom components in CSS file:
You can define reusable component classes in `web/styles/tailwind.css`:
```css
@layer components {
  .btn-primary {
    @apply px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200;
  }
}
```

Then use them in Dart:
```dart
button(classes: 'btn-primary', [text('Primary Button')])
```

## Development workflow:

1. **Start the development server:**
   ```bash
   dart run lib/main.dart
   ```

2. **After making changes to components with `@css` annotations:**
   ```bash
   dart run build_runner build
   ```

3. **The server runs at:** http://localhost:8080

## Available Tailwind features:

- ✅ All Tailwind utility classes (colors, spacing, typography, flexbox, grid, etc.)
- ✅ Responsive design classes (`sm:`, `md:`, `lg:`, `xl:`)
- ✅ State variants (`:hover`, `:focus`, `:active`, etc.)
- ✅ Custom components defined in CSS file
- ✅ Custom utilities defined in CSS file

## Examples in this project:

- **Home page**: Demonstrates responsive layout, cards, buttons, and typography
- **Counter**: Shows interactive components with hover states and transitions
- **Custom styles**: The `.card`, `.btn-primary`, and `.text-shadow` classes

## Notes:

- Since we're using the CDN version, all Tailwind classes are available
- For production, consider using a build process to purge unused CSS
- The `web/styles/tailwind.css` file is for additional custom styles only
- Jaspr's `@css` annotations can still be used alongside Tailwind classes when needed
