import 'package:jaspr/jaspr.dart';

class Counter extends StatefulComponent {
  const Counter({super.key});

  @override
  State<Counter> createState() => CounterState();
}

class CounterState extends State<Counter> {
  int count = 0;

  @override
  Iterable<Component> build(BuildContext context) sync* {
    yield div(
        classes:
            'flex items-center justify-center space-x-4 p-4 border-t-2 border-b-2 border-blue-600',
        [
          button(
            classes:
                'w-8 h-8 flex items-center justify-center rounded-full bg-transparent hover:bg-gray-100 transition-colors duration-200 text-2xl cursor-pointer',
            onClick: () {
              setState(() => count--);
            },
            [text('-')],
          ),
          span(
              classes:
                  'min-w-10 px-8 text-center text-4xl text-blue-600 font-bold',
              [text('$count')]),
          button(
            classes:
                'w-8 h-8 flex items-center justify-center rounded-full bg-transparent hover:bg-gray-100 transition-colors duration-200 text-2xl cursor-pointer',
            onClick: () {
              setState(() => count++);
            },
            [text('+')],
          ),
        ]);
  }
}
