import 'package:jaspr/jaspr.dart';
import 'heroicon.dart';

/// Component to showcase different Heroicon styles and usage patterns
class IconShowcase extends StatelessComponent {
  const IconShowcase({super.key});

  @override
  Iterable<Component> build(BuildContext context) sync* {
    yield div(classes: 'bg-white p-6 rounded-lg shadow-lg', [
      h3(classes: 'text-xl font-bold text-gray-800 mb-4', [
        text('Heroicons Showcase')
      ]),
      
      // Different sizes
      div(classes: 'mb-6', [
        h4(classes: 'text-lg font-semibold text-gray-700 mb-2', [text('Sizes')]),
        div(classes: 'flex items-center space-x-4', [
          div(classes: 'flex flex-col items-center', [
            const Heroicon.mini(HeroiconName.star, classes: 'text-yellow-500'),
            span(classes: 'text-xs text-gray-600 mt-1', [text('Mini (20px)')]),
          ]),
          div(classes: 'flex flex-col items-center', [
            const Heroicon.outline(HeroiconName.star, classes: 'text-yellow-500'),
            span(classes: 'text-xs text-gray-600 mt-1', [text('Regular (24px)')]),
          ]),
          div(classes: 'flex flex-col items-center', [
            const Heroicon.solid(HeroiconName.star, classes: 'w-8 h-8 text-yellow-500'),
            span(classes: 'text-xs text-gray-600 mt-1', [text('Large (32px)')]),
          ]),
        ]),
      ]),
      
      // Different styles
      div(classes: 'mb-6', [
        h4(classes: 'text-lg font-semibold text-gray-700 mb-2', [text('Styles')]),
        div(classes: 'grid grid-cols-3 gap-4', [
          div(classes: 'text-center', [
            const Heroicon.outline(HeroiconName.heart, classes: 'w-8 h-8 text-red-500 mx-auto mb-2'),
            span(classes: 'text-sm text-gray-600', [text('Outline')]),
          ]),
          div(classes: 'text-center', [
            const Heroicon.solid(HeroiconName.heart, classes: 'w-8 h-8 text-red-500 mx-auto mb-2'),
            span(classes: 'text-sm text-gray-600', [text('Solid')]),
          ]),
          div(classes: 'text-center', [
            const Heroicon.mini(HeroiconName.heart, classes: 'w-8 h-8 text-red-500 mx-auto mb-2'),
            span(classes: 'text-sm text-gray-600', [text('Mini')]),
          ]),
        ]),
      ]),
      
      // Common icons
      div(classes: 'mb-6', [
        h4(classes: 'text-lg font-semibold text-gray-700 mb-2', [text('Common Icons')]),
        div(classes: 'grid grid-cols-6 gap-4 text-center', [
          _iconDemo(HeroiconName.home, 'Home', 'text-blue-500'),
          _iconDemo(HeroiconName.user, 'User', 'text-green-500'),
          _iconDemo(HeroiconName.envelope, 'Email', 'text-purple-500'),
          _iconDemo(HeroiconName.magnifyingGlass, 'Search', 'text-gray-500'),
          _iconDemo(HeroiconName.cog6Tooth, 'Settings', 'text-gray-700'),
          _iconDemo(HeroiconName.plus, 'Add', 'text-indigo-500'),
        ]),
        
        // New icons showcase
        h5(classes: 'text-md font-medium text-gray-600 mt-4 mb-2', [text('New Icons (v2.1+)')]),
        div(classes: 'grid grid-cols-6 gap-4 text-center', [
          _iconDemo(HeroiconName.paintBrush, 'Paint', 'text-pink-500'),
          _iconDemo(HeroiconName.wallet, 'Wallet', 'text-yellow-600'),
          _iconDemo(HeroiconName.battery50, 'Battery', 'text-green-600'),
          _iconDemo(HeroiconName.currencyDollar, 'Dollar', 'text-green-500'),
          _iconDemo(HeroiconName.bold, 'Bold', 'text-gray-800'),
          _iconDemo(HeroiconName.calendarDays, 'Calendar', 'text-blue-600'),
        ]),
        
        // Arrow turn icons (new in v2.1.5+)
        h5(classes: 'text-md font-medium text-gray-600 mt-4 mb-2', [text('Arrow Turn Icons (v2.1.5+)')]),
        div(classes: 'grid grid-cols-4 gap-4 text-center', [
          _iconDemo(HeroiconName.arrowTurnLeftDown, 'Turn L↓', 'text-indigo-500'),
          _iconDemo(HeroiconName.arrowTurnRightDown, 'Turn R↓', 'text-indigo-500'),
          _iconDemo(HeroiconName.arrowTurnLeftUp, 'Turn L↑', 'text-indigo-500'),
          _iconDemo(HeroiconName.arrowTurnRightUp, 'Turn R↑', 'text-indigo-500'),
        ]),
      ]),
      
      // Usage examples
      div([
        h4(classes: 'text-lg font-semibold text-gray-700 mb-2', [text('Usage Examples')]),
        div(classes: 'bg-gray-50 p-4 rounded code-example', [
          pre(classes: 'text-sm', [
            text('// Solid icon\nHeroicon.solid(HeroiconName.star)\n\n'),
            text('// Outline icon with color\nHeroicon.outline(HeroiconName.heart, classes: \'text-red-500\')\n\n'),
            text('// Mini icon with custom size\nHeroicon.mini(HeroiconName.plus, classes: \'w-4 h-4\')\n\n'),
            text('// New icons from v2.1+\nHeroicon.solid(HeroiconName.paintBrush, classes: \'w-5 h-5 text-pink-500\')\n'),
            text('Heroicon.outline(HeroiconName.wallet, classes: \'w-6 h-6 text-yellow-600\')\n'),
            text('Heroicon.mini(HeroiconName.currencyDollar, classes: \'w-4 h-4 text-green-500\')')
          ]),
        ]),
      ]),
    ]);
  }

  Component _iconDemo(String iconName, String label, String colorClass) {
    return div(classes: 'flex flex-col items-center', [
      Heroicon.outline(iconName, classes: 'w-6 h-6 $colorClass mb-1'),
      span(classes: 'text-xs text-gray-600', [text(label)]),
    ]);
  }

  @css
  static List<StyleRule> get styles => [
    css('.code-example', [
      css('pre').styles(
        fontFamily: const FontFamily.list([FontFamily('Monaco'), FontFamilies.monospace]),
        fontSize: 0.875.rem,
        color: const Color('#374151'),
      ),
    ]),
  ];
}
