import 'package:jaspr/jaspr.dart';
import 'package:jaspr_router/jaspr_router.dart';

import '../constants/theme.dart';
import 'heroicon.dart';

class Header extends StatelessComponent {
  const Header({super.key});

  @override
  Iterable<Component> build(BuildContext context) sync* {
    var activePath = context.url;

    yield header([
      nav([
        for (var route in [
          (label: 'Home', path: '/', icon: HeroiconName.home),
          (label: 'About', path: '/about', icon: HeroiconName.informationCircle),
        ])
          div(classes: activePath == route.path ? 'active' : null, [
            Link(to: route.path, child: div(classes: 'nav-link', [
              Heroicon.outline(route.icon, classes: 'nav-icon'),
              text(route.label),
            ])),
          ]),
      ]),
    ]);
  }

  @css
  static List<StyleRule> get styles => [
        css('header', [
          css('&').styles(
            display: Display.flex,
            padding: Padding.all(1.em),
            justifyContent: JustifyContent.center,
          ),
          css('nav', [
            css('&').styles(
              display: Display.flex,
              height: 3.em,
              radius: BorderRadius.all(Radius.circular(10.px)),
              overflow: Overflow.clip,
              justifyContent: JustifyContent.spaceBetween,
              backgroundColor: primaryColor,
            ),
            css('a', [
              css('&').styles(
                display: Display.flex,
                height: 100.percent,
                padding: Padding.symmetric(horizontal: 2.em),
                alignItems: AlignItems.center,
                color: Colors.white,
                fontWeight: FontWeight.w700,
                textDecoration:
                    const TextDecoration(line: TextDecorationLine.none),
              ),
              css('.nav-link').styles(
                display: Display.flex,
                alignItems: AlignItems.center,
                gap: Gap.all(0.5.em),
              ),
              css('.nav-icon').styles(
                width: 1.25.em,
                height: 1.25.em,
              ),
              css('&:hover').styles(
                backgroundColor: const Color('#0005'),
              ),
            ]),
            css('div.active', [
              css('&').styles(position: const Position.relative()),
              css('&::before').styles(
                content: '',
                display: Display.block,
                position: Position.absolute(
                    bottom: 0.5.em, left: 20.px, right: 20.px),
                height: 2.px,
                radius: BorderRadius.circular(1.px),
                backgroundColor: Colors.white,
              ),
            ])
          ]),
        ]),
      ];
}
