// The entrypoint for the **server** environment.
//
// The [main] method will only be executed on the server during pre-rendering.
// To run code on the client, use the @client annotation.

// Server-specific jaspr import.
import 'package:jaspr/server.dart';

// Imports the [App] component.
import 'app.dart';

// This file is generated automatically by <PERSON><PERSON><PERSON><PERSON>, do not remove or edit.
import 'jaspr_options.dart';

void main() {
  // Initializes the server environment with the generated default options.
  Jaspr.initializeApp(
    options: defaultJasprOptions,
  );

  // Starts the app.
  //
  // [Document] renders the root document structure (<html>, <head> and <body>)
  // with the provided parameters and components.
  runApp(Document(
    title: 'nomad_spot_tw',
    styles: [
      // Import Tailwind CSS
      css.import('https://cdn.tailwindcss.com'),
      // Import Heroicons v2.2.0
      css.import('https://cdn.jsdelivr.net/npm/heroicons@2.2.0/20/solid/index.css'),
      css.import('https://cdn.jsdelivr.net/npm/heroicons@2.2.0/24/outline/index.css'),
      css.import('https://cdn.jsdelivr.net/npm/heroicons@2.2.0/24/solid/index.css'),
      // Special import rule to include to another css file.
      css.import('https://fonts.googleapis.com/css?family=Roboto'),
      // Each style rule takes a valid css selector and a set of styles.
      // Styles are defined using type-safe css bindings and can be freely chained and nested.
      css('html, body').styles(
        width: 100.percent,
        minHeight: 100.vh,
        padding: Padding.zero,
        margin: Margin.zero,
        fontFamily: const FontFamily.list(
            [FontFamily('Roboto'), FontFamilies.sansSerif]),
      ),
      css('h1').styles(
        margin: Margin.unset,
        fontSize: 4.rem,
      ),
    ],
    body: App(),
  ));
}
