import 'package:jaspr/jaspr.dart';

import '../components/counter.dart';
import '../components/heroicon.dart';

// By using the @client annotation this component will be automatically compiled to javascript and mounted
// on the client. Therefore:
// - this file and any imported file must be compilable for both server and client environments.
// - this component and any child components will be built once on the server during pre-rendering and then
//   again on the client during normal rendering.
@client
class Home extends StatefulComponent {
  const Home({super.key});

  @override
  State<Home> createState() => HomeState();
}

class HomeState extends State<Home> {
  @override
  void initState() {
    super.initState();
    // Run code depending on the rendering environment.
    if (kIsWeb) {
      print("Hello client");
      // When using @client components there is no default `main()` function on the client where you would normally
      // run any client-side initialization logic. Instead you can put it here, considering this component is only
      // mounted once at the root of your client-side component tree.
    } else {
      print("Hello server");
    }
  }

  @override
  Iterable<Component> build(BuildContext context) sync* {
    yield section(
        classes:
            'min-h-screen bg-gray-50 flex flex-col items-center justify-center p-8',
        [
          div(classes: 'card max-w-md w-full text-center', [
            img(src: 'images/logo.svg', width: 80, classes: 'mx-auto mb-6'),
            h1(
                classes: 'text-4xl font-bold text-gray-800 mb-4 text-shadow',
                [text('Welcome')]),
            p(classes: 'text-lg text-gray-600 mb-8', [
              text(
                  'You successfully created a new Jaspr site with Tailwind CSS!')
            ]),
            // Feature icons
            div(classes: 'flex justify-center space-x-8 mb-8', [
              div(classes: 'flex flex-col items-center', [
                const Heroicon.solid(HeroiconName.star, classes: 'w-8 h-8 text-yellow-500 mb-2'),
                span(classes: 'text-sm text-gray-600', [text('Fast')]),
              ]),
              div(classes: 'flex flex-col items-center', [
                const Heroicon.solid(HeroiconName.heart, classes: 'w-8 h-8 text-red-500 mb-2'),
                span(classes: 'text-sm text-gray-600', [text('Beautiful')]),
              ]),
              div(classes: 'flex flex-col items-center', [
                const Heroicon.solid(HeroiconName.checkCircle, classes: 'w-8 h-8 text-green-500 mb-2'),
                span(classes: 'text-sm text-gray-600', [text('Reliable')]),
              ]),
            ]),
            div(classes: 'space-y-4', [
              const Counter(),
              div(classes: 'flex justify-center space-x-4', [
                button(classes: 'btn-primary flex items-center gap-2', [
                  const Heroicon.solid(HeroiconName.arrowTurnRightDown, classes: 'w-4 h-4'),
                  text('Get Started'),
                ]),
                button(
                    classes:
                        'px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200 flex items-center gap-2',
                    [
                      const Heroicon.outline(HeroiconName.informationCircle, classes: 'w-4 h-4'),
                      text('Learn More'),
                    ]),
                button(
                    classes:
                        'px-4 py-2 border border-purple-300 text-purple-600 rounded-lg hover:bg-purple-50 transition-colors duration-200 flex items-center gap-2',
                    [
                      const Heroicon.outline(HeroiconName.paintBrush, classes: 'w-4 h-4'),
                      text('Customize'),
                    ]),
              ]),
            ]),
          ]),
        ]);
  }
}
