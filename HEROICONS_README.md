# Heroicons v2.2.0 in Jaspr

This project now includes **Heroicons v2.2.0**, the latest version of the beautiful SVG icon collection by the makers of Tailwind CSS, integrated seamlessly with your Jaspr application.

## What was Set Up

### 1. CDN Integration
Added **Heroicons v2.2.0** CSS imports to `lib/main.dart`:
- 20px solid icons
- 24px outline icons  
- 24px solid icons

## ✨ What's New in v2.2.0

- **React 19 Support**: Enhanced compatibility (mainly for React, doesn't affect our CSS usage)
- **Bug Fixes**: Fixed clipping path issues with `arrow-left-circle`
- **Stability**: Improved overall icon rendering

## 🆕 New Icons from Recent Versions

- **Text Editing Icons** (v2.1.5): bold, italic, underline, strikethrough, h1-h3
- **Arrow Turn Icons** (v2.1.5): Complete set of directional arrow turns
- **Currency Icons**: Support for multiple currencies (dollar, euro, pound, etc.)
- **Battery Icons**: battery-0, battery-50, battery-100
- **Tool Icons**: paint-brush, wallet, variable
- **Calendar Icons**: calendar-days, calendar-date-range

### 2. Heroicon Component (`lib/components/heroicon.dart`)
A reusable component with three variants:

```dart
// Solid 24px icon
Heroicon.solid('home', classes: 'w-6 h-6 text-blue-500')

// Outline 24px icon
Heroicon.outline('user', classes: 'w-6 h-6 text-green-500')

// Mini 20px solid icon  
Heroicon.mini('star', classes: 'w-5 h-5 text-yellow-500')
```

### 3. Icon Name Constants (`HeroiconName` class)
Pre-defined constants for common icons:
- `HeroiconName.home`
- `HeroiconName.user`
- `HeroiconName.star`
- `HeroiconName.heart`
- And many more...

### 4. Components Updated
- **Header**: Added icons to navigation links
- **Home Page**: Added feature icons and button icons
- **About Page**: Added comprehensive icon showcase

## Usage Examples

### Basic Usage
```dart
// Simple home icon
const Heroicon.solid(HeroiconName.home)

// Star with custom styling
const Heroicon.outline(HeroiconName.star, classes: 'w-8 h-8 text-yellow-500')
```

### In Buttons
```dart
button(classes: 'btn-primary flex items-center gap-2', [
  const Heroicon.solid(HeroiconName.arrowRight, classes: 'w-4 h-4'),
  text('Get Started'),
])
```

### Navigation Links
```dart
Link(to: '/home', child: div(classes: 'nav-link', [
  Heroicon.outline(HeroiconName.home, classes: 'nav-icon'),
  text('Home'),
]))
```

## Available Icon Categories

- **Navigation**: home, user, cog-6-tooth, bars-3, x-mark
- **Actions**: plus, minus, trash, pencil, check
- **Arrows**: arrow-left, arrow-right, chevron-up, chevron-down
- **Arrow Turns** ⭐ *NEW*: arrow-turn-left-down, arrow-turn-right-up, etc.
- **Communication**: envelope, phone, chat-bubble-left
- **Status**: exclamation-triangle, information-circle, check-circle
- **Objects**: magnifying-glass, heart, star, bookmark, key
- **Tools** ⭐ *NEW*: paint-brush, wallet, variable
- **Currency** ⭐ *NEW*: currency-dollar, currency-euro, currency-pound
- **Text Formatting** ⭐ *NEW*: bold, italic, underline, strikethrough
- **Battery** ⭐ *NEW*: battery-0, battery-50, battery-100
- **Calendar** ⭐ *NEW*: calendar-days, calendar-date-range

## Color & Sizing

Use Tailwind CSS classes for styling:

### Sizes
- `w-4 h-4` (16px)
- `w-5 h-5` (20px) 
- `w-6 h-6` (24px)
- `w-8 h-8` (32px)

### Colors
- `text-blue-500`
- `text-red-500`  
- `text-green-500`
- `text-gray-500`

## How Icons Work

The component generates HTML like this:
```html
<span class="hero-home-24-outline w-6 h-6 text-blue-500"></span>
```

The Heroicons CSS automatically applies the correct SVG background image based on the class name.

## Running the Project

```bash
# Development server
jaspr serve

# Build for production  
jaspr build
```

Visit the **About** page to see the comprehensive icon showcase with examples of different sizes, styles, and common icons.

## Finding More Icons

Visit [heroicons.com](https://heroicons.com) to browse the full icon library. Use the icon name (in kebab-case) with the `Heroicon` component.

Example: "User Circle" icon → `user-circle` → `HeroiconName.userCircle` or `'user-circle'`
